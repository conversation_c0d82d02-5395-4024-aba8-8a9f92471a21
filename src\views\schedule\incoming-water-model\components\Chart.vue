<template>
  <BarAndLineMix
    :dataSource="chartData"
    :markLineXAxis="showForecastBaseline ? dataSource?.reals?.length - 1 : null"
    :markPointsData="markPointsData"
  />
</template>

<script>
  import BarAndLineMix from './BarAndLineMix.vue'
  import { maxBy } from 'lodash'

  export default {
    name: 'Chart',
    props: {
      dataSource: {
        type: Object,
        required: true
      },
      showForecastBaseline: {
        type: Boolean,
        default: true
      }
    },
    components: {
      BarAndLineMix,
    },
    data() {
      return {
        chartData: [],
      }
    },
    computed: {
      markPointsData() {
        const predFlow = maxBy(this.dataSource.fcsts, 'wlv')
        const predFlowMax = {
          x:
            this.dataSource.fcsts.findIndex(el => (predFlow ? predFlow.tm === el.tm : false)) +
            this.dataSource.reals.length,
          y: predFlow?.inflow,
        }

        const predWater = maxBy(this.dataSource.fcsts, 'wlv')

        const predWaterMax = {
          x:
            this.dataSource.fcsts.findIndex(el => (predWater ? predWater.tm === el.tm : false)) +
            this.dataSource.reals.length,
          y: predWater?.wlv,
        }

        return {
          water: [{ name: '预测', value: predWaterMax.y, xAxis: predWaterMax.x, yAxis: predWaterMax.y }],
          flow: [{ value: predFlowMax.y, xAxis: predFlowMax.x, yAxis: predFlowMax.y }],
        }
      },
    },
    watch: {
      dataSource: {
        handler(newVal) {
          const data = newVal
          console.log('data', data)

          const rainData = {
            name: '时段雨量',
            data: data.reals.map(el => [el.tm, el.rain]).concat(data.fcsts.map(el => [el.tm, el.rain])),
          }
          const sumRainData = {
            name: '累计降雨量',
            data: rainData.data.map((el, idx) => {
              const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
              return [el[0], +sum.toFixed(1)]
            }),
          }

          this.chartData = [
            rainData,
            sumRainData,
            {
              name: '水位',
              data: data.reals.map(el => [el.tm, el.wlv]).concat(data.fcsts.map(el => [el.tm, el.wlv])),
            },
            {
              name: '来水流量',
              data: data.reals.map(el => [el.tm, el.inflow]).concat(data.fcsts.map(el => [el.tm, el.inflow])),
            },
            {
              name: '出库流量',
              data: data.reals.map(el => [el.tm, el.outflow]).concat(data.fcsts.map(el => [el.tm, el.outflow])),
            },
          ]
        },
        deep: true,
        immediate: true,
      },
    },
    created() {},
  }
</script>

<style lang="less" scoped></style>
